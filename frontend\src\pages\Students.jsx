import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FadeIn, StaggerContainer, StaggerItem } from '../components/ui/AnimatedComponents';

const Students = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');

  // Sample data untuk pengumuman
  const announcements = [
    {
      id: 1,
      title: 'Pengumuman Libur Semester Ganjil 2024/2025',
      category: 'akademik',
      date: '2025-01-15',
      priority: 'high',
      content: 'Libur semester ganjil akan dimulai pada tanggal 20 Januari 2025 dan masuk kembali pada tanggal 3 Februari 2025. Siswa diharapkan menggunakan waktu libur untuk belajar mandiri.',
      author: '<PERSON><PERSON><PERSON>',
      views: 245,
      isNew: true
    },
    {
      id: 2,
      title: 'Pendaftaran Ekstrakurikuler Semester Genap',
      category: 'ekstrakurikuler',
      date: '2025-01-10',
      priority: 'medium',
      content: 'Pendaftaran ekstrakurikuler untuk semester genap dibuka mulai tanggal 15 Januari 2025. Siswa dapat mendaftar maksimal 2 ekstrakurikuler.',
      author: 'Wakil Kepala Sekolah',
      views: 189,
      isNew: true
    },
    {
      id: 3,
      title: 'Jadwal Ujian Tengah Semester (UTS)',
      category: 'akademik',
      date: '2025-01-08',
      priority: 'high',
      content: 'UTS akan dilaksanakan pada tanggal 25-29 Januari 2025. Siswa diharapkan mempersiapkan diri dengan baik dan mengikuti semua mata pelajaran yang diujikan.',
      author: 'Wakil Kepala Sekolah',
      views: 312,
      isNew: false
    },
    {
      id: 4,
      title: 'Lomba Karya Tulis Ilmiah Tingkat Sekolah',
      category: 'kompetisi',
      date: '2025-01-05',
      priority: 'medium',
      content: 'Lomba karya tulis ilmiah akan diadakan pada bulan Februari 2025. Pendaftaran dibuka hingga tanggal 20 Januari 2025.',
      author: 'Guru Pembimbing',
      views: 156,
      isNew: false
    },
    {
      id: 5,
      title: 'Perubahan Jadwal Pelajaran Sementara',
      category: 'akademik',
      date: '2025-01-03',
      priority: 'medium',
      content: 'Terdapat perubahan jadwal pelajaran sementara untuk kelas X, XI, dan XII mulai tanggal 10 Januari 2025 karena ada kegiatan workshop guru.',
      author: 'Tata Usaha',
      views: 203,
      isNew: false
    },
    {
      id: 6,
      title: 'Kegiatan Bakti Sosial Sekolah',
      category: 'kegiatan',
      date: '2024-12-28',
      priority: 'low',
      content: 'Kegiatan bakti sosial akan dilaksanakan pada tanggal 15 Februari 2025. Siswa yang berminat dapat mendaftar di ruang OSIS.',
      author: 'Pembina OSIS',
      views: 98,
      isNew: false
    }
  ];

  const categories = [
    { id: 'all', name: 'Semua', icon: '📋' },
    { id: 'akademik', name: 'Akademik', icon: '📚' },
    { id: 'ekstrakurikuler', name: 'Ekstrakurikuler', icon: '🏆' },
    { id: 'kompetisi', name: 'Kompetisi', icon: '🥇' },
    { id: 'kegiatan', name: 'Kegiatan', icon: '🎉' }
  ];

  const filteredAnnouncements = selectedCategory === 'all' 
    ? announcements 
    : announcements.filter(announcement => announcement.category === selectedCategory);

  const formatDate = (dateString) => {
    const options = { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric',
      timeZone: 'Asia/Jakarta'
    };
    return new Date(dateString).toLocaleDateString('id-ID', options);
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPriorityText = (priority) => {
    switch (priority) {
      case 'high': return 'Penting';
      case 'medium': return 'Sedang';
      case 'low': return 'Biasa';
      default: return 'Normal';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Section */}
      <FadeIn direction="down" className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              📢 Pengumuman Sekolah
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Informasi terbaru dan pengumuman penting untuk seluruh siswa
            </p>
          </div>
        </div>
      </FadeIn>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Category Filter - Responsive */}
        <FadeIn delay={0.2} className="mb-8">
          <div className="bg-white rounded-lg shadow-sm p-4 md:p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Filter Kategori</h2>
            
            {/* Desktop Filter (>= 900px) */}
            <div className="hidden min-[900px]:flex flex-wrap gap-3">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`flex items-center px-4 py-2 rounded-lg font-medium transition-all duration-300 ${
                    selectedCategory === category.id
                      ? 'bg-blue-600 text-white shadow-lg transform scale-105'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200 hover:scale-105'
                  }`}
                >
                  <span className="mr-2">{category.icon}</span>
                  {category.name}
                </button>
              ))}
            </div>

            {/* Mobile Filter (< 900px) */}
            <div className="min-[900px]:hidden">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.icon} {category.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </FadeIn>

        {/* Announcements Grid - Responsive */}
        <StaggerContainer className="grid grid-cols-1 min-[900px]:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredAnnouncements.map((announcement, index) => (
            <StaggerItem key={announcement.id} delay={index * 0.1}>
              <motion.div
                whileHover={{ y: -5, scale: 1.02 }}
                className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 border border-gray-100"
              >
                {/* Card Header */}
                <div className="p-6 pb-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getPriorityColor(announcement.priority)}`}>
                        {getPriorityText(announcement.priority)}
                      </span>
                      {announcement.isNew && (
                        <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium border border-blue-200">
                          Baru
                        </span>
                      )}
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-500">{formatDate(announcement.date)}</p>
                    </div>
                  </div>
                  
                  <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2 hover:text-blue-600 transition-colors">
                    {announcement.title}
                  </h3>
                  
                  <p className="text-gray-600 text-sm leading-relaxed line-clamp-3 mb-4">
                    {announcement.content}
                  </p>
                </div>

                {/* Card Footer */}
                <div className="px-6 py-4 bg-gray-50 border-t border-gray-100">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center text-sm text-gray-500">
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                      {announcement.author}
                    </div>
                    <div className="flex items-center text-sm text-gray-500">
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      {announcement.views}
                    </div>
                  </div>
                </div>
              </motion.div>
            </StaggerItem>
          ))}
        </StaggerContainer>

        {/* Empty State */}
        {filteredAnnouncements.length === 0 && (
          <FadeIn className="text-center py-12">
            <div className="bg-white rounded-xl shadow-lg p-8 max-w-md mx-auto">
              <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Tidak Ada Pengumuman</h3>
              <p className="text-gray-600">
                Belum ada pengumuman untuk kategori yang dipilih.
              </p>
            </div>
          </FadeIn>
        )}
      </div>
    </div>
  );
};

export default Students;
